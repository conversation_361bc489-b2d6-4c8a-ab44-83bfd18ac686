#include <bits/stdc++.h>
using namespace std;
using ll = long long;

struct ItemState {
    int pos;     // current station
    ll time;     // current time
    int dest;    // destination station
};

int main() {
    ios::sync_with_stdio(false);
    cin.tie(nullptr);
    int N, M, K;
    ll P, B, C;
    if (!(cin >> N >> M >> K >> P >> B >> C)) return 0;
    vector<int> A(N);
    for (int i = 0; i < N; ++i) cin >> A[i];

    // Build mapping of segments to linear indices.
    // segmentList[idx] = pair(item, station s) meaning segment is from s -> s+1 for that item.
    vector<pair<int,int>> segList;
    vector<vector<int>> segIdx(N, vector<int>(M+1, -1)); // segIdx[item][s] = index in segList for segment s->s+1
    for (int i = 0; i < N; ++i) {
        for (int s = 1; s <= A[i]-1; ++s) {
            segIdx[i][s] = (int)segList.size();
            segList.push_back({i, s});
        }
    }
    int S = (int)segList.size();
    ll best = LLONG_MAX;

    // Enumerate all choices (0 = bike, 1 = train) over S segments
    // (only feasible for small S).
    long long totalMasks = 1LL << S;
    for (long long mask = 0; mask < totalMasks; ++mask) {
        // initialize item states
        vector<ItemState> items(N);
        for (int i = 0; i < N; ++i) {
            items[i].pos = 1;
            items[i].time = 0;
            items[i].dest = A[i];
        }
        int doneCount = 0;

        // helper to get choice for item i at station s (segment s->s+1)
        auto choice_is_train = [&](int i, int s)->bool {
            int id = segIdx[i][s];
            if (id < 0) return false;
            return ((mask >> id) & 1LL) != 0;
        };

        // process bikes up to time T: repeatedly apply any bike-steps whose start time <= T
        auto process_bikes_up_to = [&](ll T) {
            bool changed = true;
            while (changed) {
                changed = false;
                for (int i = 0; i < N; ++i) {
                    if (items[i].pos >= items[i].dest) continue; // already arrived
                    int s = items[i].pos;
                    if (!choice_is_train(i, s)) {
                        // bike chosen for this segment
                        if (items[i].time <= T) {
                            // perform the bike move
                            items[i].time += C;
                            items[i].pos += 1;
                            changed = true;
                            if (items[i].pos >= items[i].dest) {
                                ++doneCount;
                            }
                        }
                    }
                }
            }
        };

        // simulate trains k = 0,1,2,... until all items are done
        long long k = 0;
        // safety limit to avoid infinite loops in pathological cases (shouldn't happen)
        const long long MAX_K = 1LL << 20; // large but finite
        while (doneCount < N && k < MAX_K) {
            // For train k, process stations s = 1 .. M-1 in increasing arrival time
            ll base_depart = k * P;
            for (int s = 1; s <= M-1 && doneCount < N; ++s) {
                ll T = base_depart + (ll)(s-1) * B; // train k arrival at station s

                // process bike moves that can finish before or at time T
                process_bikes_up_to(T);

                // find waiting items at station s who want to take train for this segment and arrived <= T
                vector<pair<pair<ll,int>,int>> waiting; // ((arrival_time, id), item_index)
                for (int i = 0; i < N; ++i) {
                    if (items[i].pos == s && items[i].pos < items[i].dest) {
                        if (choice_is_train(i, s) && items[i].time <= T) {
                            waiting.push_back({{items[i].time, i}, i});
                        }
                    }
                }
                // sort by arrival time then by id
                sort(waiting.begin(), waiting.end());

                // board up to K items on this train (train's capacity resets per k)
                int cap = K;
                for (auto &w : waiting) {
                    if (cap == 0) break;
                    int i = w.second;
                    // board this item: it will arrive next station at T + B
                    items[i].time = T + B;
                    items[i].pos += 1;
                    if (items[i].pos >= items[i].dest) ++doneCount;
                    --cap;
                }
                // Note: items that couldn't board remain at station s and will wait for future trains or bike moves.
            }

            // After finishing stations for this train k, increment k
            ++k;

            // Small optimization: also process bike moves up to next train's earliest arrival (which is k*P + 0*B = k*P)
            // This is optional, but helps items move forward before next train
            ll next_train_start = k * P;
            process_bikes_up_to(next_train_start);
        }

        if (doneCount < N) {
            // didn't finish within MAX_K trains; skip this mask
            continue;
        }

        // compute total time
        ll total = 0;
        for (int i = 0; i < N; ++i) total += items[i].time;
        best = min(best, total);
    }

    if (best == LLONG_MAX) cout << "-1\n";
    else cout << best << "\n";
    return 0;
}
