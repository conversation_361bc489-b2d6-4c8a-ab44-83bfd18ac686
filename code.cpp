#include <bits/stdc++.h>
using namespace std;

#define ll long long
#define vi vector<int>
#define pii pair<int, int>
#define vvi vector<vi>
#define vii vector<pii>
#define ull unsigned long long
#define endl "\n"
#define __TOISETHIVOI__ signed main()
#define all(x) x.begin(), x.end()
#define pb push_back
#define mp make_pair
#define fast ios_base::sync_with_stdio(0); cin.tie(0); cout.tie(0);
#define setp(x) fixed << setprecision(x)
#define setp2(x, y) fixed << setprecision(x) << y
#define fi first
#define se second
#define rall(x) (x).rbegin(), (x).rend()
#define sz(x) (int)(x).size()
#define yes cout << "YES\n"
#define no cout << "NO\n"
#define YESNO(x) cout << ((x) ? "YES\n" : "NO\n")

const int MOD = 1e9 + 7;
const int INF = INT_MAX;
const int N = 2005; // đủ lớn cho n, m nhỏ

void sol()
{
    int n, m;
    cin >> n >> m;
    vvi a(n + 1, vi(m + 1, 0));
    vi time(n + 1);
    int maxTime = 0;
    for (int i = 1; i <= n; i++)
    {
        cin >> time[i];
        int k;
        cin >> k;
        for (int j = 1; j <= k; j++)
        {
            int x;
            cin >> x;
            maxTime = max(maxTime, x);
            if (x >= 1 && x <= m) a[i][x] = 1;
        }
    }
    vvi f(n + 1, vi(m + 1, 0));
    for (int i = 1; i <= n; i++)
    {
        for (int j = 1; j <= m; j++)
        {
            f[i][j] = f[i - 1][j] + f[i][j - 1] - f[i - 1][j - 1] + a[i][j];
        }
    }

    vi dp(maxTime + 1, 0);
    for (int i = 1; i <= maxTime; i++)
    {
        dp[i] = dp[i - 1]; 
        for (int j = 0; j < i; j++)
        {
            if (i - j >= time[i]) 
            {
                int v = f[i][m] - f[j][m] - f[i][0] + f[j][0];
                dp[i] = max(dp[i], dp[j] + v);
            }
        }
    }
    cout << *max_element(all(dp)) << endl;
}

__TOISETHIVOI__
{
    fast;
    int t = 1;
    // cin >> t;
    while (t--)
        sol();
}
